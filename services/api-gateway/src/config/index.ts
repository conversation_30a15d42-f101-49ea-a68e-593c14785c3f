import { config } from 'dotenv';
import { z } from 'zod';

// Load environment variables
config();

// Environment variable schema for validation
const envSchema = z.object({
  // Server Configuration
  PORT: z.string().transform(Number).default('3000'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // Service Discovery - Backend Services
  USER_MANAGEMENT_SERVICE_URL: z.string().url().default('http://localhost:3002'),
  DATA_INGESTION_SERVICE_URL: z.string().url().default('http://localhost:3001'),
  ANALYSIS_ENGINE_SERVICE_URL: z.string().url().default('http://localhost:3003'),

  // Security Configuration
  JWT_SECRET: z.string().min(32).optional(),
  JWT_PUBLIC_KEY_PATH: z.string().optional(),
  JWT_ALGORITHM: z.enum(['HS256', 'RS256']).default('RS256'),
  CORS_ORIGIN: z.string().default('http://localhost:3000'),

  // Redis Configuration
  REDIS_URL: z.string().default('redis://localhost:6379'),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.string().transform(Number).default('0'),
  REDIS_TTL: z.string().transform(Number).default('3600'),

  // Rate Limiting Configuration
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('60000'),
  RATE_LIMIT_FREE_TIER: z.string().transform(Number).default('30'),
  RATE_LIMIT_PREMIUM_TIER: z.string().transform(Number).default('100'),
  RATE_LIMIT_PRO_TIER: z.string().transform(Number).default('300'),

  // Logging Configuration
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FORMAT: z.enum(['combined', 'common', 'dev', 'short', 'tiny']).default('combined'),

  // Health Check Configuration
  HEALTH_CHECK_TIMEOUT: z.string().transform(Number).default('5000'),

  // Request Timeout Configuration
  REQUEST_TIMEOUT: z.string().transform(Number).default('30000'),
  PROXY_TIMEOUT: z.string().transform(Number).default('30000'),

  // Circuit Breaker Configuration
  CIRCUIT_BREAKER_THRESHOLD: z.string().transform(Number).default('5'),
  CIRCUIT_BREAKER_TIMEOUT: z.string().transform(Number).default('60000'),

  // Gateway Metadata
  GATEWAY_VERSION: z.string().default('1.0.0'),
  API_VERSION: z.string().default('v1'),
});

// Validate and parse environment variables
const env = envSchema.parse(process.env);

// Service configuration
export const serviceConfig = {
  userManagement: {
    url: env.USER_MANAGEMENT_SERVICE_URL,
    healthPath: '/api/v1/health',  // Updated to correct path
    timeout: env.PROXY_TIMEOUT,
  },
  dataIngestion: {
    url: env.DATA_INGESTION_SERVICE_URL,
    healthPath: '/api/v1/health',  // Updated to correct path
    timeout: env.PROXY_TIMEOUT,
  },
  analysisEngine: {
    url: env.ANALYSIS_ENGINE_SERVICE_URL,
    healthPath: '/api/v1/health',  // Fixed to match Analysis Engine endpoint
    timeout: env.PROXY_TIMEOUT,
  },
} as const;

// Gateway configuration
export const gatewayConfig = {
  server: {
    port: env.PORT,
    environment: env.NODE_ENV,
    version: env.GATEWAY_VERSION,
    apiVersion: env.API_VERSION,
  },
  
  security: {
    jwt: {
      secret: env.JWT_SECRET,
      publicKeyPath: env.JWT_PUBLIC_KEY_PATH,
      algorithm: env.JWT_ALGORITHM,
    },
    cors: {
      origin: env.CORS_ORIGIN.split(',').map(origin => origin.trim()),
      credentials: true,
      optionsSuccessStatus: 200,
    },
  },

  redis: {
    url: env.REDIS_URL,
    password: env.REDIS_PASSWORD,
    db: env.REDIS_DB,
    ttl: env.REDIS_TTL,
    connectTimeout: 10000,
    lazyConnect: true,
    maxRetriesPerRequest: 3,
  },

  rateLimiting: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    tiers: {
      free: env.RATE_LIMIT_FREE_TIER,
      premium: env.RATE_LIMIT_PREMIUM_TIER,
      pro: env.RATE_LIMIT_PRO_TIER,
    },
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  },

  logging: {
    level: env.LOG_LEVEL,
    format: env.LOG_FORMAT,
  },

  healthCheck: {
    timeout: env.HEALTH_CHECK_TIMEOUT,
    interval: 30000, // 30 seconds
  },

  circuitBreaker: {
    threshold: env.CIRCUIT_BREAKER_THRESHOLD,
    timeout: env.CIRCUIT_BREAKER_TIMEOUT,
  },

  timeouts: {
    request: env.REQUEST_TIMEOUT,
    proxy: env.PROXY_TIMEOUT,
  },
} as const;

// Route mappings for service discovery
export const routeConfig = {
  // Authentication and user management routes
  '/api/v1/auth': 'userManagement',
  '/api/v1/users': 'userManagement',
  '/api/v1/profile': 'userManagement',
  
  // Data ingestion routes (admin only)
  '/api/v1/data': 'dataIngestion',
  '/api/v1/admin': 'dataIngestion',
  
  // Analysis engine routes
  '/api/v1/analysis': 'analysisEngine',
  '/api/v1/expected-points': 'analysisEngine',
  '/api/v1/transfers': 'analysisEngine',
  '/api/v1/players': 'analysisEngine',
  '/api/v1/teams': 'analysisEngine',
  '/api/v1/fixtures': 'analysisEngine',
} as const;

// Export types for TypeScript
export type ServiceName = keyof typeof serviceConfig;
export type RoutePath = keyof typeof routeConfig;
export type Environment = typeof env.NODE_ENV;
export type LogLevel = typeof env.LOG_LEVEL;
export type SubscriptionTier = 'free' | 'premium' | 'pro';

// Validate required configuration
if (env.NODE_ENV === 'production') {
  if (!env.JWT_SECRET && !env.JWT_PUBLIC_KEY_PATH) {
    throw new Error('JWT_SECRET or JWT_PUBLIC_KEY_PATH must be provided in production');
  }
}

export default gatewayConfig;