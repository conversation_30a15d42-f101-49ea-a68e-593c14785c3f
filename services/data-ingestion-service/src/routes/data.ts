import { Router, Request, Response } from 'express';
import { dataOrchestratorService } from '@/services/data-orchestrator';
import { getDataStorageService } from '@/services/data-storage';
import { createLogger } from '@/utils/logger';
import { ApiResponse, SyncResult } from '@/types';

const router = Router();
const log = createLogger('DataRoutes');

/**
 * Trigger manual data collection for a specific source
 */
router.post('/collect/:source', async (req: Request, res: Response) => {
  const { source } = req.params;
  const validSources = ['fpl', 'elite-managers', 'fbref', 'understat', 'betting-odds'];

  try {
    if (!validSources.includes(source)) {
      res.status(400).json({
        success: false,
        error: `Invalid source. Valid sources: ${validSources.join(', ')}`,
        timestamp: new Date(),
      } as ApiResponse<null>);
      return;
    }

    log.info('Manual data collection triggered', { source, triggeredBy: 'api' });

    const result = await dataOrchestratorService.triggerDataCollection(source);

    res.json({
      success: true,
      data: result,
      timestamp: new Date(),
    } as ApiResponse<SyncResult>);
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : String(err);
    const stack = err instanceof Error ? err.stack : undefined;
    log.error('Manual data collection failed', {
      source,
      error: errorMessage,
      stack,
    });

    res.status(500).json({
      success: false,
      error: errorMessage,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Trigger data collection for all enabled sources
 */
router.post('/collect/all', async (_req: Request, res: Response) => {
  try {
    log.info('Full data collection triggered', { triggeredBy: 'api' });

    const results: Record<string, SyncResult> = {};
    const sources = ['fpl', 'elite-managers', 'fbref'];

    for (const source of sources) {
      try {
        results[source] = await dataOrchestratorService.triggerDataCollection(source);
      } catch (errInLoop) {
        const loopErrorMessage = errInLoop instanceof Error ? errInLoop.message : String(errInLoop);
        const loopStack = errInLoop instanceof Error ? errInLoop.stack : undefined;
        log.error('Source collection failed during full sync', {
          source,
          error: loopErrorMessage,
          stack: loopStack,
        });
        results[source] = {
          success: false,
          recordsProcessed: 0,
          recordsUpdated: 0,
          recordsInserted: 0,
          errors: [loopErrorMessage],
          duration: 0,
          timestamp: new Date(),
        };
      }
    }

    const overallSuccess = Object.values(results).every(r => r.success);

    res.json({
      success: overallSuccess,
      data: results,
      timestamp: new Date(),
    } as ApiResponse<Record<string, SyncResult>>);
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : String(err);
    const stack = err instanceof Error ? err.stack : undefined;
    log.error('Full data collection failed', {
      error: errorMessage,
      stack,
    });

    res.status(500).json({
      success: false,
      error: errorMessage,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get orchestrator status
 */
router.get('/status', (_req: Request, res: Response) => {
  try {
    const status = dataOrchestratorService.getStatus();

    res.json({
      success: true,
      data: status,
      timestamp: new Date(),
    } as ApiResponse<typeof status>);
  } catch (error) {
    log.error('Failed to get orchestrator status', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get data freshness information
 */
router.get('/freshness', async (_req: Request, res: Response) => {
  try {
    const reports = await dataOrchestratorService.monitorDataFreshness();

    res.json({
      success: true,
      data: reports,
      timestamp: new Date(),
    } as ApiResponse<typeof reports>);
  } catch (error) {
    log.error('Failed to get data freshness', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get data freshness for a specific source
 */
router.get('/freshness/:source', async (req: Request, res: Response) => {
  const { source } = req.params;

  try {
    const lastUpdate = await getDataStorageService().getDataFreshness(source);

    if (!lastUpdate) {
      res.status(404).json({
        success: false,
        error: `No data freshness information found for source: ${source}`,
        timestamp: new Date(),
      } as ApiResponse<null>);
      return;
    }

    const ageSeconds = Math.floor((Date.now() - lastUpdate.getTime()) / 1000);

    res.json({
      success: true,
      data: {
        source,
        lastUpdate,
        ageSeconds,
        ageHuman: formatDuration(ageSeconds),
      },
      timestamp: new Date(),
    } as ApiResponse<{
      source: string;
      lastUpdate: Date;
      ageSeconds: number;
      ageHuman: string;
    }>);
  } catch (error) {
    log.error('Failed to get source freshness', {
      source,
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Run data quality validation
 */
router.post('/validate', async (_req: Request, res: Response) => {
  try {
    log.info('Data quality validation triggered', { triggeredBy: 'api' });

    const report = await dataOrchestratorService.validateDataQuality();

    res.json({
      success: true,
      data: report,
      timestamp: new Date(),
    } as ApiResponse<typeof report>);
  } catch (error) {
    log.error('Data quality validation failed', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get FPL players data
 */
router.get('/players', async (_req: Request, res: Response) => {
  try {
    log.info('FPL players data requested', { triggeredBy: 'api' });

    const dataStorage = getDataStorageService();
    const players = await dataStorage.getFPLPlayers();

    res.json({
      success: true,
      data: players,
      timestamp: new Date(),
    } as ApiResponse<typeof players>);
  } catch (error) {
    log.error('Failed to get FPL players', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get FPL teams data
 */
router.get('/teams', async (_req: Request, res: Response) => {
  try {
    log.info('FPL teams data requested', { triggeredBy: 'api' });

    const dataStorage = getDataStorageService();
    const teams = await dataStorage.getFPLTeams();

    res.json({
      success: true,
      data: teams,
      timestamp: new Date(),
    } as ApiResponse<typeof teams>);
  } catch (error) {
    log.error('Failed to get FPL teams', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get FPL gameweeks data
 */
router.get('/gameweeks', async (_req: Request, res: Response) => {
  try {
    log.info('FPL gameweeks data requested', { triggeredBy: 'api' });

    const dataStorage = getDataStorageService();
    const gameweeks = await dataStorage.getFPLGameweeks();

    res.json({
      success: true,
      data: gameweeks,
      timestamp: new Date(),
    } as ApiResponse<typeof gameweeks>);
  } catch (error) {
    log.error('Failed to get FPL gameweeks', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get collection history/logs
 */
router.get('/history', async (_req: Request, res: Response) => {
  try {
    // TODO: Implement collection history retrieval from database
    // This would show recent sync results, errors, and performance metrics

    const mockHistory = {
      recentCollections: [],
      totalCollections: 0,
      successRate: 0,
      averageDuration: 0,
    };

    res.json({
      success: true,
      data: mockHistory,
      timestamp: new Date(),
    } as ApiResponse<typeof mockHistory>);
  } catch (error) {
    log.error('Failed to get collection history', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Get performance metrics
 */
router.get('/metrics', async (_req: Request, res: Response) => {
  try {
    // TODO: Implement performance metrics collection
    // This would include response times, success rates, data volumes, etc.
    
    const mockMetrics = {
      avgResponseTime: 0,
      successRate: 0,
      recordsProcessedToday: 0,
      errorRate: 0,
      lastError: null,
    };

    res.json({
      success: true,
      data: mockMetrics,
      timestamp: new Date(),
    } as ApiResponse<typeof mockMetrics>);
  } catch (error) {
    log.error('Failed to get performance metrics', {
      error: (error as Error).message,
    });

    res.status(500).json({
      success: false,
      error: (error as Error).message,
      timestamp: new Date(),
    } as ApiResponse<null>);
  }
});

/**
 * Helper function to format duration in human-readable format
 */
function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds} seconds`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  } else if (seconds < 86400) {
    const hours = Math.floor(seconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else {
    const days = Math.floor(seconds / 86400);
    return `${days} day${days > 1 ? 's' : ''}`;
  }
}

export { router as dataRoutes };