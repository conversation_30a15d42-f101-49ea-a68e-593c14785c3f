import { Pool } from 'pg';
import { Db, Collection } from 'mongodb';
import { getPostgresPool, getMongoDb, withPostgresTransaction } from '@/utils/database';
import { createLogger, PerformanceTimer } from '@/utils/logger';
import {
  Player,
  Team,
  Fixture,
  // Gameweek, // Unused
  EliteManager,
  // OwnershipData, // Unused
  FBRefPlayerStats,
  UnderstatPlayerData,
  BettingOdds,
  // ConsensusProbability, // Unused
  SyncResult,
  DataIngestionError,
  // DataIngestionJob, // Unused
} from '@/types';

const log = createLogger('DataStorage');

export class DataStorageService {
  private pgPool: Pool;
  private mongoDb: Db;

  constructor() {
    this.pgPool = getPostgresPool();
    this.mongoDb = getMongoDb();
    
    log.info('Data Storage Service initialized');
  }

  /**
   * Store FPL players data in PostgreSQL
   */
  async storePlayers(players: Player[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storePlayers', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      await withPostgresTransaction(async (client) => {
        for (const player of players) {
          try {
            // First, delete existing player data for this fpl_id to handle updates
            await client.query('DELETE FROM players WHERE fpl_id = $1', [player.fplId]);

            // Then insert the new data
            const result = await client.query(`
              INSERT INTO players (
                fpl_id, web_name, first_name, second_name, team_id, position_id,
                current_price, status, news, chance_of_playing_this_round,
                chance_of_playing_next_round, selected_by_percent, transfers_in,
                transfers_out, transfers_in_event, transfers_out_event,
                total_points, points_per_game, form, goals_scored, assists,
                clean_sheets, goals_conceded, own_goals, penalties_saved,
                penalties_missed, yellow_cards, red_cards, saves, bonus, bps,
                influence, creativity, threat, ict_index, starts, expected_goals,
                expected_assists, expected_goal_involvements, expected_goals_conceded,
                last_updated
              ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
                $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28,
                $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41
              )
            `, [
              player.fplId, player.webName, player.firstName, player.secondName,
              player.teamId, player.positionId, player.currentPrice, player.status,
              player.news, player.chanceOfPlayingThisRound, player.chanceOfPlayingNextRound,
              player.selectedByPercent, player.transfersIn, player.transfersOut,
              player.transfersInEvent, player.transfersOutEvent, player.totalPoints,
              player.pointsPerGame, player.form, player.goalsScored, player.assists,
              player.cleanSheets, player.goalsConceded, player.ownGoals,
              player.penaltiesSaved, player.penaltiesMissed, player.yellowCards,
              player.redCards, player.saves, player.bonus, player.bps,
              player.influence, player.creativity, player.threat, player.ictIndex,
              player.starts, player.expectedGoals, player.expectedAssists,
              player.expectedGoalInvolvements, player.expectedGoalsConceded,
              player.lastUpdated,
            ]);

            if (result.rowCount === 1) {
              recordsInserted++;
            } else {
              recordsUpdated++;
            }
          } catch (loopErr) {
            const errorMsg = `Failed to store player ${player.fplId}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
            errors.push(errorMsg);
            log.warn(errorMsg);
          }
        }
      });

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: players.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('Players data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      const result: SyncResult = {
        success: false,
        recordsProcessed: players.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };

      log.error('Failed to store players data', { error: errorMessage });
      return result;
    }
  }

  /**
   * Store teams data in PostgreSQL
   */
  async storeTeams(teams: Team[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storeTeams', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      await withPostgresTransaction(async (client) => {
        for (const team of teams) {
          try {
            const result = await client.query(`
              INSERT INTO teams (fpl_id, name, short_name, code)
              VALUES ($1, $2, $3, $4)
              ON CONFLICT (fpl_id) DO UPDATE SET
                name = EXCLUDED.name,
                short_name = EXCLUDED.short_name,
                code = EXCLUDED.code,
                updated_at = NOW()
            `, [team.fplId, team.name, team.shortName, team.code]);

            if (result.rowCount === 1) {
              recordsInserted++;
            } else {
              recordsUpdated++;
            }
          } catch (loopErr) {
            const errorMsg = `Failed to store team ${team.fplId}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
            errors.push(errorMsg);
            log.warn(errorMsg);
          }
        }
      });

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: teams.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('Teams data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      log.error('Failed to store teams data', { error: errorMessage });
      return {
        success: false,
        recordsProcessed: teams.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Store fixtures data in PostgreSQL
   */
  async storeFixtures(fixtures: Fixture[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storeFixtures', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      await withPostgresTransaction(async (client) => {
        for (const fixture of fixtures) {
          try {
            const result = await client.query(`
              INSERT INTO fixtures (
                fpl_id, gameweek_id, team_h, team_a, kickoff_time,
                finished, team_h_score, team_a_score
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
              ON CONFLICT (fpl_id) DO UPDATE SET
                gameweek_id = EXCLUDED.gameweek_id,
                team_h = EXCLUDED.team_h,
                team_a = EXCLUDED.team_a,
                kickoff_time = EXCLUDED.kickoff_time,
                finished = EXCLUDED.finished,
                team_h_score = EXCLUDED.team_h_score,
                team_a_score = EXCLUDED.team_a_score,
                updated_at = NOW()
            `, [
              fixture.fplId, fixture.gameweekId, fixture.teamH, fixture.teamA,
              fixture.kickoffTime, fixture.finished, fixture.teamHScore, fixture.teamAScore,
            ]);

            if (result.rowCount === 1) {
              recordsInserted++;
            } else {
              recordsUpdated++;
            }
          } catch (loopErr) {
            const errorMsg = `Failed to store fixture ${fixture.fplId}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
            errors.push(errorMsg);
            log.warn(errorMsg);
          }
        }
      });

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: fixtures.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('Fixtures data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      log.error('Failed to store fixtures data', { error: errorMessage });
      return {
        success: false,
        recordsProcessed: fixtures.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Store gameweeks data in PostgreSQL
   */
  async storeGameweeks(gameweeks: any[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storeGameweeks', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      await withPostgresTransaction(async (client) => {
        for (const gameweek of gameweeks) {
          try {
            const result = await client.query(`
              INSERT INTO gameweeks (
                id, name, deadline_time, average_entry_score, finished, data_checked,
                highest_scoring_entry, highest_score, is_current, is_next, is_previous,
                most_selected, most_transferred_in, top_element, transfers_made
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
              ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                deadline_time = EXCLUDED.deadline_time,
                average_entry_score = EXCLUDED.average_entry_score,
                finished = EXCLUDED.finished,
                data_checked = EXCLUDED.data_checked,
                highest_scoring_entry = EXCLUDED.highest_scoring_entry,
                highest_score = EXCLUDED.highest_score,
                is_current = EXCLUDED.is_current,
                is_next = EXCLUDED.is_next,
                is_previous = EXCLUDED.is_previous,
                most_selected = EXCLUDED.most_selected,
                most_transferred_in = EXCLUDED.most_transferred_in,
                top_element = EXCLUDED.top_element,
                transfers_made = EXCLUDED.transfers_made,
                updated_at = NOW()
            `, [
              gameweek.id, gameweek.name, gameweek.deadlineTime, gameweek.averageEntryScore || 0,
              gameweek.finished || false, gameweek.dataChecked || false, gameweek.highestScoringEntry || 0,
              gameweek.highestScore || 0, gameweek.isCurrent || false, gameweek.isNext || false,
              gameweek.isPrevious || false, gameweek.mostSelected || 0, gameweek.mostTransferredIn || 0,
              gameweek.topElement || 0, gameweek.transfersMade || 0
            ]);

            if (result.rowCount === 1) {
              recordsInserted++;
            } else {
              recordsUpdated++;
            }
          } catch (loopErr) {
            const errorMsg = `Failed to store gameweek ${gameweek.id}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
            errors.push(errorMsg);
            log.warn(errorMsg);
          }
        }
      });

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: gameweeks.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('Gameweeks data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      log.error('Failed to store gameweeks data', { error: errorMessage });
      return {
        success: false,
        recordsProcessed: gameweeks.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Store elite managers data in MongoDB (flexible schema)
   */
  async storeEliteManagers(eliteManagers: EliteManager[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storeEliteManagers', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      const collection: Collection = this.mongoDb.collection('elite_managers');
      
      for (const manager of eliteManagers) {
        try {
          const result = await collection.replaceOne(
            { fplId: manager.fplId },
            manager,
            { upsert: true }
          );

          if (result.upsertedCount > 0) {
            recordsInserted++;
          } else if (result.modifiedCount > 0) {
            recordsUpdated++;
          }
        } catch (loopErr) {
          const errorMsg = `Failed to store elite manager ${manager.fplId}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
          errors.push(errorMsg);
          log.warn(errorMsg);
        }
      }

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: eliteManagers.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('Elite managers data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      log.error('Failed to store elite managers data', { error: errorMessage });
      return {
        success: false,
        recordsProcessed: eliteManagers.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Store FBRef player stats in MongoDB
   */
  async storeFBRefStats(stats: FBRefPlayerStats[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storeFBRefStats', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      const collection: Collection = this.mongoDb.collection('fbref_stats');
      
      for (const stat of stats) {
        try {
          const result = await collection.replaceOne(
            {
              playerId: stat.playerId,
              season: stat.season,
            },
            stat,
            { upsert: true }
          );

          if (result.upsertedCount > 0) {
            recordsInserted++;
          } else if (result.modifiedCount > 0) {
            recordsUpdated++;
          }
        } catch (loopErr) {
          const errorMsg = `Failed to store FBRef stats for player ${stat.playerId}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
          errors.push(errorMsg);
          log.warn(errorMsg);
        }
      }

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: stats.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('FBRef stats data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      log.error('Failed to store FBRef stats', { error: errorMessage });
      return {
        success: false,
        recordsProcessed: stats.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Store Understat player data in MongoDB
   */
  async storeUnderstatData(data: UnderstatPlayerData[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storeUnderstatData', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      const collection: Collection = this.mongoDb.collection('understat_data');
      
      for (const playerData of data) {
        try {
          const result = await collection.replaceOne(
            {
              playerId: playerData.playerId,
              season: playerData.season,
            },
            playerData,
            { upsert: true }
          );

          if (result.upsertedCount > 0) {
            recordsInserted++;
          } else if (result.modifiedCount > 0) {
            recordsUpdated++;
          }
        } catch (loopErr) {
          const errorMsg = `Failed to store Understat data for player ${playerData.playerId}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
          errors.push(errorMsg);
          log.warn(errorMsg);
        }
      }

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: data.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('Understat data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      log.error('Failed to store Understat data', { error: errorMessage });
      return {
        success: false,
        recordsProcessed: data.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Store betting odds in MongoDB
   */
  async storeBettingOdds(odds: BettingOdds[]): Promise<SyncResult> {
    const timer = new PerformanceTimer('storeBettingOdds', log);
    const errors: string[] = [];
    let recordsUpdated = 0;
    let recordsInserted = 0;

    try {
      const collection: Collection = this.mongoDb.collection('betting_odds');
      
      for (const oddsData of odds) {
        try {
          const result = await collection.replaceOne(
            {
              playerId: oddsData.playerId,
              gameweekId: oddsData.gameweekId,
              bookmaker: oddsData.bookmaker,
              marketType: oddsData.marketType,
            },
            oddsData,
            { upsert: true }
          );

          if (result.upsertedCount > 0) {
            recordsInserted++;
          } else if (result.modifiedCount > 0) {
            recordsUpdated++;
          }
        } catch (loopErr) {
          const errorMsg = `Failed to store betting odds for player ${oddsData.playerId}: ${loopErr instanceof Error ? loopErr.message : String(loopErr)}`;
          errors.push(errorMsg);
          log.warn(errorMsg);
        }
      }

      const duration = timer.end();
      const result: SyncResult = {
        success: errors.length === 0,
        recordsProcessed: odds.length,
        recordsUpdated,
        recordsInserted,
        errors,
        duration,
        timestamp: new Date(),
      };

      log.info('Betting odds data stored', result);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      log.error('Failed to store betting odds', { error: errorMessage });
      return {
        success: false,
        recordsProcessed: odds.length,
        recordsUpdated,
        recordsInserted,
        errors: [errorMessage],
        duration,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Get all FPL players for player matching
   */
  async getAllFPLPlayers(): Promise<Player[]> {
    const timer = new PerformanceTimer('getAllFPLPlayers', log);

    try {
      const result = await this.pgPool.query(`
        SELECT
          fpl_id, web_name, first_name, second_name, team_id, position_id,
          current_price, status, total_points
        FROM players
        ORDER BY fpl_id
      `);

      timer.end({ playersCount: result.rows.length });

      return result.rows.map(row => ({
        id: `fpl_${row.fpl_id}`,
        fplId: row.fpl_id,
        webName: row.web_name,
        firstName: row.first_name,
        secondName: row.second_name,
        teamId: row.team_id,
        positionId: row.position_id,
        currentPrice: row.current_price,
        status: row.status,
        totalPoints: row.total_points,
        // Add other required fields with defaults for this use case
      } as Player));
    } catch (err) {
      timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new DataIngestionError(
        `Failed to get FPL players: ${errorMessage}`,
        'GET_FPL_PLAYERS_ERROR',
        'postgresql'
      );
    }
  }

  /**
   * Get FPL players (alias for getAllFPLPlayers for API consistency)
   */
  async getFPLPlayers(): Promise<Player[]> {
    return this.getAllFPLPlayers();
  }

  /**
   * Get all FPL teams
   */
  async getFPLTeams(): Promise<Team[]> {
    const timer = new PerformanceTimer('getFPLTeams', log);

    try {
      const result = await this.pgPool.query(`
        SELECT
          fpl_id, name, short_name, code
        FROM teams
        ORDER BY fpl_id
      `);

      timer.end({ teamsCount: result.rows.length });

      return result.rows.map(row => ({
        id: `fpl_${row.fpl_id}`,
        fplId: row.fpl_id,
        name: row.name,
        shortName: row.short_name,
        code: row.code,
        // Add default values for missing fields
        strength: 3,
        strengthOverallHome: 3,
        strengthOverallAway: 3,
        strengthAttackHome: 3,
        strengthAttackAway: 3,
        strengthDefenceHome: 3,
        strengthDefenceAway: 3,
      } as Team));
    } catch (err) {
      timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new DataIngestionError(
        `Failed to get FPL teams: ${errorMessage}`,
        'GET_FPL_TEAMS_ERROR',
        'postgresql'
      );
    }
  }

  /**
   * Get all FPL gameweeks
   */
  async getFPLGameweeks(): Promise<any[]> {
    const timer = new PerformanceTimer('getFPLGameweeks', log);

    try {
      const result = await this.pgPool.query(`
        SELECT
          id, name, deadline_time, is_previous, is_current, is_next,
          finished, data_checked, highest_scoring_entry, highest_score,
          most_selected, most_transferred_in, top_element, transfers_made
        FROM gameweeks
        ORDER BY id
      `);

      timer.end({ gameweeksCount: result.rows.length });

      return result.rows.map(row => ({
        id: `fpl_${row.id}`,
        fplId: row.id,
        name: row.name,
        deadlineTime: row.deadline_time,
        isPrevious: row.is_previous,
        isCurrent: row.is_current,
        isNext: row.is_next,
        finished: row.finished,
        dataChecked: row.data_checked,
        highestScoringEntry: row.highest_scoring_entry,
        highestScore: row.highest_score,
        mostSelected: row.most_selected,
        mostTransferredIn: row.most_transferred_in,
        topElement: row.top_element,
        transfersMade: row.transfers_made,
        // Add default values for missing fields
        deadlineTimeEpoch: null,
        deadlineTimeGameOffset: null,
        isCupMatch: false,
        cupLeaguesCreated: false,
        h2hKoMatchesCreated: false,
        chipPlays: [],
        mostCaptained: null,
        mostViceCaptained: null,
      }));
    } catch (err) {
      timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new DataIngestionError(
        `Failed to get FPL gameweeks: ${errorMessage}`,
        'GET_FPL_GAMEWEEKS_ERROR',
        'postgresql'
      );
    }
  }

  /**
   * Update data freshness tracking
   */
  async updateDataFreshness(source: string): Promise<void> {
    try {
      await this.pgPool.query(`
        INSERT INTO data_freshness (source, last_updated)
        VALUES ($1, NOW())
        ON CONFLICT (source) DO UPDATE SET
          last_updated = NOW()
      `, [source]);

      log.debug('Data freshness updated', { source });
    } catch (err) {
      log.warn('Failed to update data freshness', {
        source,
        error: err instanceof Error ? err.message : String(err),
      });
    }
  }

  /**
   * Get data freshness information
   */
  async getDataFreshness(source: string): Promise<Date | null> {
    try {
      const result = await this.pgPool.query(
        'SELECT last_updated FROM data_freshness WHERE source = $1',
        [source]
      );

      return result.rows.length > 0 ? result.rows[0].last_updated : null;
    } catch (err) {
      log.warn('Failed to get data freshness', {
        source,
        error: err instanceof Error ? err.message : String(err),
      });
      return null;
    }
  }
}

// Singleton instance - will be created only when needed
let _dataStorageService: DataStorageService | null = null;

export const getDataStorageService = (): DataStorageService => {
  if (!_dataStorageService) {
    _dataStorageService = new DataStorageService();
  }
  return _dataStorageService;
};