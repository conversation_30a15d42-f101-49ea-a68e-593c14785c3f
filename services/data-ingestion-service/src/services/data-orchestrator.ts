import { <PERSON><PERSON><PERSON><PERSON> } from 'cron';
import { config } from '@/config';
import { createLogger, PerformanceTimer } from '@/utils/logger';
import { fplApiService } from '@/services/fpl-api';
import { fbrefScraperService } from '@/services/fbref-scraper';
import { getDataStorageService } from '@/services/data-storage';
import {
  SyncResult,
  DataQualityReport,
  DataFreshnessReport,
  DataIngestionError,
} from '@/types';

const log = createLogger('DataOrchestrator');

export class DataOrchestratorService {
  private jobs: Map<string, CronJob> = new Map();
  private isRunning = false;

  constructor() {
    log.info('Data Orchestrator Service initialized');
  }

  /**
   * Start all scheduled data collection jobs
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      log.warn('Data orchestrator already running');
      return;
    }

    log.info('Starting data orchestrator...');

    try {
      // Schedule FPL data collection (every hour)
      if (config.features.enableFplDataCollection) {
        this.scheduleJob('fpl-data', config.schedule.fplData, async () => { await this.collectFPLData(); });
      }

      // Schedule elite manager tracking (daily after deadline)
      if (config.features.enableEliteManagerTracking) {
        this.scheduleJob('elite-managers', config.schedule.eliteManagers, async () => { await this.collectEliteManagers(); });
      }

      // Schedule FBRef scraping (weekly)
      if (config.features.enableFbrefScraping) {
        this.scheduleJob('fbref-data', config.schedule.fbrefData, async () => { await this.collectFBRefData(); });
      }

      // Schedule Understat scraping (weekly)
      if (config.features.enableUnderstatScraping) {
        this.scheduleJob('understat-data', config.schedule.understatData, async () => { await this.collectUnderstatData(); });
      }

      // Schedule betting odds collection (every 30 minutes)
      if (config.features.enableBettingOddsCollection) {
        this.scheduleJob('betting-odds', config.schedule.bettingOdds, async () => { await this.collectBettingOdds(); });
      }

      // Start all jobs
      this.jobs.forEach((job, name) => {
        job.start();
        log.info('Scheduled job started', { jobName: name });
      });

      this.isRunning = true;
      log.info('Data orchestrator started successfully', {
        activeJobs: this.jobs.size,
      });

      // Run immediate data collection on startup
      await this.runInitialDataCollection();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      const stack = err instanceof Error ? err.stack : undefined;
      log.error('Failed to start data orchestrator', {
        error: errorMessage,
        stack,
      });
      // Re-throw the original error if it's an Error instance, otherwise wrap it
      if (err instanceof Error) throw err;
      throw new Error(errorMessage);
    }
  }

  /**
   * Stop all scheduled jobs
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      log.warn('Data orchestrator not running');
      return;
    }

    log.info('Stopping data orchestrator...');

    this.jobs.forEach((job, name) => {
      job.stop();
      log.info('Scheduled job stopped', { jobName: name });
    });

    this.jobs.clear();
    this.isRunning = false;

    log.info('Data orchestrator stopped successfully');
  }

  /**
   * Schedule a cron job
   */
  private scheduleJob(name: string, cronExpression: string, handler: () => Promise<void>): void {
    const job = new CronJob(
      cronExpression,
      async () => {
        log.jobStarted(name, 'scheduled', 'medium');
        const timer = new PerformanceTimer(`job-${name}`, log);

        try {
          await handler();
          const duration = timer.end();
          log.jobCompleted(name, 'scheduled', duration);
        } catch (err) {
          const duration = timer.end();
          let jobError: Error;
          const errorMessage = err instanceof Error ? err.message : String(err);
          
          if (err instanceof Error) {
            jobError = err;
            // Augment message if not already a DataIngestionError which might have context
            if (!(err instanceof DataIngestionError)) {
              jobError.message = `${err.message} (Job duration before error: ${duration}ms)`;
            }
          } else {
            jobError = new Error(`${errorMessage} (Job duration before error: ${duration}ms)`);
          }
          log.jobFailed(name, 'scheduled', jobError, 0); // jobFailed expects an Error object
        }
      },
      null,
      false,
      'UTC'
    );

    this.jobs.set(name, job);
    log.info('Job scheduled', { jobName: name, cronExpression });
  }

  /**
   * Run initial data collection on startup
   */
  private async runInitialDataCollection(): Promise<void> {
    log.info('Running initial data collection...');

    try {
      // Always collect FPL data first as it's required for other sources
      if (config.features.enableFplDataCollection) {
        await this.collectFPLData();
      }

      // Collect other data sources in parallel if enabled
      const parallelJobs: Promise<void>[] = [];

      if (config.features.enableEliteManagerTracking) {
        parallelJobs.push((async () => { await this.collectEliteManagers(); })());
      }

      if (config.features.enableFbrefScraping) {
        parallelJobs.push((async () => { await this.collectFBRefData(); })());
      }

      if (parallelJobs.length > 0) {
        await Promise.allSettled(parallelJobs);
      }

      log.info('Initial data collection completed');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      const stack = err instanceof Error ? err.stack : undefined;
      log.error('Initial data collection failed', {
        error: errorMessage,
        stack,
      });
      // Not re-throwing here as it's a background task within startup
    }
  }

  /**
   * Collect FPL data
   */
  async collectFPLData(): Promise<SyncResult> {
    log.syncStart('fpl-api');
    const timer = new PerformanceTimer('collectFPLData', log);

    try {
      // Fetch bootstrap data (players, teams, gameweeks)
      const { players, teams, gameweeks } = await fplApiService.fetchBootstrapData();
      
      // Fetch fixtures
      const fixtures = await fplApiService.fetchFixtures();

      // Store data in parallel
      const storeOperations = [
        { name: 'players', promise: getDataStorageService().storePlayers(players) },
        { name: 'teams', promise: getDataStorageService().storeTeams(teams) },
        { name: 'gameweeks', promise: getDataStorageService().storeGameweeks(gameweeks) },
        { name: 'fixtures', promise: getDataStorageService().storeFixtures(fixtures) },
      ];
      
      const storeResults = await Promise.allSettled(storeOperations.map(op => op.promise));
      const syncErrors: string[] = [];

      storeResults.forEach((result, index) => {
        if (result.status === 'rejected') {
          const opName = storeOperations[index].name;
          const errorMsg = `Failed to store ${opName} data: ${result.reason instanceof Error ? result.reason.message : String(result.reason)}`;
          log.error(errorMsg, {
            stack: result.reason instanceof Error ? result.reason.stack : undefined,
          });
          syncErrors.push(errorMsg);
        }
      });

      // Check for current live gameweek
      const currentGameweek = gameweeks.find(gw => gw.isCurrent);
      if (currentGameweek && currentGameweek.finished === false) {
        try {
          const liveData = await fplApiService.fetchLiveGameweekData(currentGameweek.id);
          // TODO: Store liveData. For now, just log its collection.
          log.info('Live gameweek data collected and ready for storage', {
            gameweek: currentGameweek.id,
            elementsCount: liveData?.elements?.length
          });
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : String(err);
          const stack = err instanceof Error ? err.stack : undefined;
          const errorMsg = `Failed to collect live gameweek data: ${errorMessage}`;
          log.warn(errorMsg, {
            gameweek: currentGameweek.id,
            error: errorMessage,
            stack,
          });
          syncErrors.push(errorMsg);
        }
      }

      // Update data freshness
      await getDataStorageService().updateDataFreshness('fpl_api');

      const duration = timer.end();
      const totalRecords = players.length + teams.length + fixtures.length;
      
      const result: SyncResult = {
        success: syncErrors.length === 0,
        recordsProcessed: totalRecords,
        // TODO: Populate recordsUpdated and recordsInserted from storeResults if they return detailed info
        recordsUpdated: 0,
        recordsInserted: 0,
        errors: syncErrors,
        duration,
        timestamp: new Date(),
      };

      if (result.success) {
        log.syncComplete('fpl-api', duration, totalRecords);
      } else {
        log.syncComplete('fpl-api', duration, totalRecords, syncErrors);
      }
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      const errorToLog = err instanceof Error ? err : new Error(errorMessage); // Ensure an Error object is logged
      log.syncError('fpl-api', errorToLog, duration);
      
      throw new DataIngestionError(
        `FPL data collection failed: ${errorMessage}`,
        'FPL_COLLECTION_ERROR',
        'fpl-api',
        true // retryable
      );
    }
  }

  /**
   * Collect elite managers data
   */
  async collectEliteManagers(): Promise<SyncResult> {
    log.syncStart('elite-managers');
    const timer = new PerformanceTimer('collectEliteManagers', log);

    try {
      const eliteManagers = await fplApiService.fetchEliteManagers();
      const result = await getDataStorageService().storeEliteManagers(eliteManagers);

      await getDataStorageService().updateDataFreshness('elite_managers');

      const duration = timer.end();
      log.syncComplete('elite-managers', duration, eliteManagers.length);
      
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      const errorToLog = err instanceof Error ? err : new Error(errorMessage);
      log.syncError('elite-managers', errorToLog, duration);
      
      throw new DataIngestionError(
        `Elite managers collection failed: ${errorMessage}`,
        'ELITE_MANAGERS_ERROR',
        'fpl-api',
        true // retryable
      );
    }
  }

  /**
   * Collect FBRef data
   */
  async collectFBRefData(): Promise<SyncResult> {
    log.syncStart('fbref');
    const timer = new PerformanceTimer('collectFBRefData', log);

    try {
      // Get FPL players for matching
      const fplPlayers = await getDataStorageService().getAllFPLPlayers();
      
      // Fetch FBRef player stats
      const fbrefPlayers = await fbrefScraperService.fetchPlayerStats();
      
      // Match players and process data
      const { matched, result: matchingResult } = await fbrefScraperService.matchPlayersWithFPL(
        fbrefPlayers,
        fplPlayers
      );

      // Store matched data
      const result = await getDataStorageService().storeFBRefStats(matched);

      await getDataStorageService().updateDataFreshness('fbref');

      const duration = timer.end();
      log.syncComplete('fbref', duration, matched.length, [
        `Matching rate: ${(matchingResult.matchRate * 100).toFixed(1)}%`,
        `Unmatched: ${matchingResult.unmatched.length}`,
      ]);

      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      const errorToLog = err instanceof Error ? err : new Error(errorMessage);
      log.syncError('fbref', errorToLog, duration);
      
      // Corrected: DataIngestionError takes 4 arguments max
      throw new DataIngestionError(
        `FBRef data collection failed: ${errorMessage}`,
        'FBREF_COLLECTION_ERROR',
        'fbref',
        true // retryable
      );
    }
  }

  /**
   * Collect Understat data (placeholder)
   */
  async collectUnderstatData(): Promise<SyncResult> {
    log.syncStart('understat');
    const timer = new PerformanceTimer('collectUnderstatData', log);

    try {
      // TODO: Implement Understat scraping service
      log.warn('Understat data collection not yet implemented');
      
      await getDataStorageService().updateDataFreshness('understat');

      const duration = timer.end();
      const result: SyncResult = {
        success: true,
        recordsProcessed: 0,
        recordsUpdated: 0,
        recordsInserted: 0,
        errors: ['Not implemented'],
        duration,
        timestamp: new Date(),
      };

      log.syncComplete('understat', duration, 0, ['Not implemented']);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      const errorToLog = err instanceof Error ? err : new Error(errorMessage);
      log.syncError('understat', errorToLog, duration);
      
      throw new DataIngestionError(
        `Understat data collection failed: ${errorMessage}`,
        'UNDERSTAT_COLLECTION_ERROR',
        'understat',
        true // retryable
      );
    }
  }

  /**
   * Collect betting odds data (placeholder)
   */
  async collectBettingOdds(): Promise<SyncResult> {
    log.syncStart('betting-odds');
    const timer = new PerformanceTimer('collectBettingOdds', log);

    try {
      // TODO: Implement betting odds collection
      log.warn('Betting odds collection not yet implemented');
      
      await getDataStorageService().updateDataFreshness('betting_odds');

      const duration = timer.end();
      const result: SyncResult = {
        success: true,
        recordsProcessed: 0,
        recordsUpdated: 0,
        recordsInserted: 0,
        errors: ['Not implemented'],
        duration,
        timestamp: new Date(),
      };

      log.syncComplete('betting-odds', duration, 0, ['Not implemented']);
      return result;
    } catch (err) {
      const duration = timer.end();
      const errorMessage = err instanceof Error ? err.message : String(err);
      const errorToLog = err instanceof Error ? err : new Error(errorMessage);
      log.syncError('betting-odds', errorToLog, duration);
      
      throw new DataIngestionError(
        `Betting odds collection failed: ${errorMessage}`,
        'BETTING_ODDS_ERROR',
        'betting-apis',
        true // retryable
      );
    }
  }

  /**
   * Manual trigger for data collection
   */
  async triggerDataCollection(source: string): Promise<SyncResult> {
    log.info('Manual data collection triggered', { source });

    switch (source) {
      case 'fpl':
        return await this.collectFPLData();
      case 'elite-managers':
        return await this.collectEliteManagers();
      case 'fbref':
        return await this.collectFBRefData();
      case 'understat':
        return await this.collectUnderstatData();
      case 'betting-odds':
        return await this.collectBettingOdds();
      default:
        throw new DataIngestionError(
          `Unknown data source: ${source}`,
          'UNKNOWN_SOURCE_ERROR',
          source
        );
    }
  }

  /**
   * Get orchestrator status
   */
  getStatus(): {
    isRunning: boolean;
    activeJobs: string[];
    lastRun?: Date;
  } {
    return {
      isRunning: this.isRunning,
      activeJobs: Array.from(this.jobs.keys()),
    };
  }

  /**
   * Validate cross-source data quality
   */
  async validateDataQuality(): Promise<DataQualityReport> {
    log.info('Running data quality validation...');
    const timer = new PerformanceTimer('validateDataQuality', log);

    try {
      // TODO: Implement cross-source validation logic
      // This would compare data between FPL, FBRef, and Understat sources
      // to identify discrepancies and data quality issues

      const duration = timer.end();
      
      const report: DataQualityReport = {
        source: 'cross-validation',
        totalIssues: 0,
        highSeverityIssues: 0,
        mediumSeverityIssues: 0,
        dataQualityScore: 1.0,
        issues: [],
        generatedAt: new Date(),
      };

      log.info('Data quality validation completed', {
        score: report.dataQualityScore,
        issues: report.totalIssues,
        duration,
      });

      return report;
    } catch (err) {
      timer.end(); // Ensure timer is ended even on error
      const errorMessage = err instanceof Error ? err.message : String(err);
      const stack = err instanceof Error ? err.stack : undefined;
      log.error('Data quality validation failed', {
        error: errorMessage,
        stack,
      });
      // Re-throw the original error if it's an Error instance, otherwise wrap it
      if (err instanceof Error) throw err;
      throw new Error(errorMessage);
    }
  }

  /**
   * Monitor data freshness across all sources
   */
  async monitorDataFreshness(): Promise<DataFreshnessReport[]> {
    log.info('Monitoring data freshness...');

    const sources = ['fpl_api', 'elite_managers', 'fbref', 'understat', 'betting_odds'];
    const thresholds = {
      fpl_api: 3600, // 1 hour
      elite_managers: 86400, // 24 hours
      fbref: 604800, // 1 week
      understat: 604800, // 1 week
      betting_odds: 1800, // 30 minutes
    };

    const reports: DataFreshnessReport[] = [];
    const currentTime = new Date();

    for (const source of sources) {
      try {
        const lastUpdate = await getDataStorageService().getDataFreshness(source);
        const ageSeconds = lastUpdate 
          ? Math.floor((currentTime.getTime() - lastUpdate.getTime()) / 1000)
          : Infinity;
        
        const thresholdSeconds = thresholds[source as keyof typeof thresholds];
        const isStale = ageSeconds > thresholdSeconds;
        
        const report: DataFreshnessReport = {
          source,
          lastUpdate: lastUpdate || new Date(0),
          ageSeconds,
          thresholdSeconds,
          isStale,
          stalenessRatio: ageSeconds / thresholdSeconds,
        };

        reports.push(report);

        if (isStale) {
          log.warn('Stale data detected', report);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        const stack = err instanceof Error ? err.stack : undefined;
        log.error('Failed to check data freshness', {
          source,
          error: errorMessage,
          stack,
        });
      }
    }

    log.info('Data freshness monitoring completed', {
      totalSources: reports.length,
      staleSources: reports.filter(r => r.isStale).length,
    });

    return reports;
  }
}

export const dataOrchestratorService = new DataOrchestratorService();