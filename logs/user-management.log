
> fpl-analytics-user-management-service@1.0.0 dev
> ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts

[INFO] 15:46:18 ts-node-dev ver. 2.0.0 (using ts-node ver. 10.9.2, typescript ver. 5.8.3)
15:46:20 [[31merror[39m]: Failed to connect to database {
  "error": "password authentication failed for user \"postgres\""
}
15:46:20 [[31merror[39m]: Failed to start server {
  "error": "password authentication failed for user \"postgres\"",
  "stack": "error: password authentication failed for user \"postgres\"\n    at /home/<USER>/projects/fplreview_clone/services/user-management-service/node_modules/pg-pool/index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Database.connect (/home/<USER>/projects/fplreview_clone/services/user-management-service/src/utils/database.ts:34:22)\n    at startServer (/home/<USER>/projects/fplreview_clone/services/user-management-service/src/index.ts:151:5)"
}
