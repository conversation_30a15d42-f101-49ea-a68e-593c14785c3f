#!/usr/bin/env node

/**
 * Authentication Flow Test Script
 * Tests the complete authentication flow for FPL Analytics Platform
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3002/api/v1';
const FRONTEND_URL = 'http://localhost:3040';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

// New test user for registration
const NEW_TEST_USER = {
  firstName: 'Test',
  lastName: 'User',
  email: `test.${Date.now()}@example.com`,
  password: 'TestPassword123!',
  terms_accepted: true
};

async function testHealthEndpoints() {
  console.log('\n🔍 Testing Health Endpoints...');
  
  try {
    // Test User Management Service
    const userMgmtHealth = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ User Management Service:', userMgmtHealth.status, userMgmtHealth.data.status);
    
    // Test API Gateway
    const gatewayHealth = await axios.get('http://localhost:3000/health').catch(e => ({ status: 'error', data: e.message }));
    console.log('✅ API Gateway:', gatewayHealth.status === 'error' ? 'Error' : gatewayHealth.status);
    
    // Test Frontend
    const frontendResponse = await axios.get(FRONTEND_URL).catch(e => ({ status: 'error' }));
    console.log('✅ Frontend:', frontendResponse.status === 'error' ? 'Error' : frontendResponse.status);
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
}

async function testExistingUserLogin() {
  console.log('\n🔐 Testing Existing User Login...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: TEST_USER.email,
      password: TEST_USER.password
    });
    
    console.log('✅ Login successful:', response.status);
    console.log('✅ Response structure:', {
      hasUser: !!response.data.data?.user,
      hasTokens: !!response.data.data?.tokens,
      tokenType: response.data.data?.tokens?.token_type,
      expiresIn: response.data.data?.tokens?.expires_in
    });
    
    return response.data.data.tokens.access_token;
    
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    return null;
  }
}

async function testProtectedEndpoint(token) {
  console.log('\n🛡️ Testing Protected Endpoint...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/profile/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Protected endpoint access successful:', response.status);
    console.log('✅ User profile:', {
      id: response.data.data?.id,
      email: response.data.data?.email,
      subscriptionTier: response.data.data?.subscription_tier
    });
    
  } catch (error) {
    console.error('❌ Protected endpoint failed:', error.response?.data || error.message);
  }
}

async function testUserRegistration() {
  console.log('\n📝 Testing User Registration...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/register`, NEW_TEST_USER);
    
    console.log('✅ Registration successful:', response.status);
    console.log('✅ New user created:', {
      hasUser: !!response.data.data?.user,
      email: response.data.data?.user?.email,
      hasTokens: !!response.data.data?.tokens
    });
    
    return response.data.data.tokens.access_token;
    
  } catch (error) {
    console.error('❌ Registration failed:', error.response?.data || error.message);
    return null;
  }
}

async function testInvalidLogin() {
  console.log('\n❌ Testing Invalid Login...');
  
  try {
    await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    
    console.log('❌ Invalid login should have failed but succeeded');
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Invalid login correctly rejected:', error.response.status);
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
    }
  }
}

async function runTests() {
  console.log('🚀 FPL Analytics Authentication Flow Test');
  console.log('==========================================');
  
  await testHealthEndpoints();
  
  const loginToken = await testExistingUserLogin();
  if (loginToken) {
    await testProtectedEndpoint(loginToken);
  }
  
  const registrationToken = await testUserRegistration();
  if (registrationToken) {
    await testProtectedEndpoint(registrationToken);
  }
  
  await testInvalidLogin();
  
  console.log('\n✅ Authentication flow tests completed!');
  console.log('\n📋 Next Steps:');
  console.log('1. Open browser to http://localhost:3040');
  console.log('2. Test registration form with new user');
  console.log('3. Test login form with existing user');
  console.log('4. Check browser developer tools for API calls');
  console.log('5. Verify JWT token storage in localStorage');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testExistingUserLogin, testUserRegistration };
